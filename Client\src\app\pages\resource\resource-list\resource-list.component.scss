tbody td {
  font-family: "Helvetica", "Helvetica Neue", sans-serif;
  font-size: 14px;
  color: #818990;
  line-height: 20px;
  letter-spacing: normal;
  font-style: normal;
  font-weight: normal;
}
.table-head {
  background-color: #2a6ca7;
}
.table-head th {
  padding-left: 14px;
  color: #fff;
}
:host ::ng-deep {
  .p-inputtext,
  .p-select-label,
  .p-dropdown,
  .p-dropdown-label,
  .p-icon {
    color: #818990 !important;
  }
}

.userInput {
  height: 38px;
  border: 1px solid #dddddd;
}

.custom-row-lines th,
.custom-row-lines td {
  border-top: none;
  border-right: none;
  border-left: none;
  border-bottom: 1px solid #dee2e6;
}

.custom-row-lines thead th {
  border-bottom: 2px solid #2a6ca7;
}

.custom-row-lines tr:last-child td {
  border-radius: 4% 4% 0 0; /* Rounded corners for the last row */
}

a {
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

a:hover {
  background-color: #d0d7df; /* Darker shade for hover */
  color: #ffffff; /* Ensure text is visible */
}

:host {
  // Table container without scrolling - using standard pagination only
  .table-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
  }

  .table-responsive {
    // Remove scrolling - use pagination instead
    overflow: visible;
  }

  // Sticky header
  .sticky-top {
    position: sticky;
    top: 0;
    z-index: 1;
  }

  .table {
    td {
      padding: 1rem 0.75rem; // Increased vertical padding for table cells
      vertical-align: middle;
    }

    img.img-fluid {
      width: 50px;
      height: 50px;
      object-fit: cover;
      border: 1px solid #dee2e6;
    }

    // Action buttons styling - matching user list component
    .action-buttons {
      padding: 0.5rem 0.25rem;

      .d-flex {
        gap: 0.25rem !important;
      }

      .btn {
        margin: 0;
        padding: 0.25rem !important;
        min-width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        i {
          font-size: 1rem;
        }
      }
    }
  }

  // Compact filter styles
  .card-body.py-2 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .form-label.small {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
  }

  // Clear button specific styling to match event-list pattern
  .clear-btn {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
    background-color: transparent !important;

    &:hover {
      background-color: rgba(220, 53, 69, 0.04) !important;
      border-color: #c82333 !important;
      color: #c82333 !important;
    }

    &:focus {
      box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.2) !important;
    }
  }

  // Button styling for consistency
  .filter-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 38px;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .form-control-sm,
    .form-select-sm,
    .btn-sm {
      font-size: 0.875rem;
      padding: 0.25rem 0.5rem;
    }
  }

  // Consistent form control styling
  .p-inputtext,
  .p-dropdown {
    width: 100%;
    height: 38px;
    border: 1px solid #dddddd !important;
  }

  // Fix for title field alignment and icon placement
  .p-input-icon-left {
    display: block;
    position: relative;
    width: 100%;

    i {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 0.75rem;
      z-index: 1;
      color: #6c757d;
    }

    input {
      width: 100%;
      padding-left: 2.5rem;
    }
  }

  // Ensure dropdown and input have same height
  .p-dropdown {
    display: flex;
    align-items: center;

    .p-dropdown-label {
      padding-top: 0.4rem;
      padding-bottom: 0.4rem;
    }
  }

  // Consistent spacing
  .card-body.py-2 {
    padding: 1rem;
  }

  .form-label.small {
    margin-bottom: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #495057;
  }

  // Responsive adjustments
  @media (max-width: 768px) {
    .row.g-3 > div {
      margin-bottom: 0.5rem;
    }
  }
}

// Additional styling for consistent form control appearance
:host ::ng-deep {
  // Placeholder text color for all input types
  .p-inputtext::placeholder,
  .p-calendar .p-inputtext::placeholder {
    color: #818990 !important;
  }

  // Form control icons styling
  .p-input-icon-left i,
  .p-dropdown-trigger,
  .p-calendar .p-datepicker-trigger,
  .p-inputtext-clear-icon,
  .p-calendar-clear-icon {
    color: #818990 !important;
  }

  // Calendar field border styling
  .p-calendar .p-inputtext {
    border: 1px solid #dddddd !important;
  }

  // Dropdown and input text color
  .p-inputtext,
  .p-select-label,
  .p-dropdown,
  .p-dropdown-label {
    color: #818990 !important;
  }
}

// Sortable header styling
.sortable-header {
  user-select: none;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  i {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    transition: transform 0.2s ease;
  }
}
